[0.000000] (-) TimerEvent: {}
[0.000405] (pkg_gazebo) JobQueued: {'identifier': 'pkg_gazebo', 'dependencies': OrderedDict()}
[0.000516] (pkg_gazebo) JobStarted: {'identifier': 'pkg_gazebo'}
[0.006739] (pkg_gazebo) JobProgress: {'identifier': 'pkg_gazebo', 'progress': 'cmake'}
[0.007102] (pkg_gazebo) JobProgress: {'identifier': 'pkg_gazebo', 'progress': 'build'}
[0.007521] (pkg_gazebo) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/robot_1/build/pkg_gazebo', '--', '-j32', '-l32'], 'cwd': '/home/<USER>/robot_1/build/pkg_gazebo', 'env': OrderedDict({'GJS_DEBUG_TOPICS': 'JS ERROR;JS LOG', 'PYTHON_BASIC_REPL': '1', 'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'zh_CN:zh_HK:en', 'USER': 'ling', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'wayland', 'GIT_ASKPASS': '/usr/share/code/resources/app/extensions/git/dist/askpass.sh', 'CLUTTER_DISABLE_MIPMAPPED_TEXT': '1', 'SHLVL': '2', 'LD_LIBRARY_PATH': '/home/<USER>/ros2_ws/install/tide_msgs/lib:/home/<USER>/dev_ws/install/learning_interface/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'LESS': '-FX', 'HOME': '/home/<USER>', 'CHROME_DESKTOP': 'code.desktop', 'TERM_PROGRAM_VERSION': '1.101.0', 'DESKTOP_SESSION': 'ubuntu', 'GIO_LAUNCHED_DESKTOP_FILE': '/usr/share/applications/code.desktop', 'ROS_PYTHON_VERSION': '3', 'GNOME_SHELL_SESSION_MODE': 'ubuntu', 'GTK_MODULES': 'gail:atk-bridge', 'PAGER': 'cat', 'VSCODE_GIT_ASKPASS_MAIN': '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js', 'LC_MONETARY': 'en_US.UTF-8', 'VSCODE_GIT_ASKPASS_NODE': '/usr/share/code/code', 'MANAGERPID': '383655', 'PYDEVD_DISABLE_FILE_VALIDATION': '1', 'SYSTEMD_EXEC_PID': '383931', 'BUNDLED_DEBUGPY_PATH': '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy', 'IM_CONFIG_CHECK_ENV': '1', 'GSM_SKIP_SSH_AGENT_WORKAROUND': 'true', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'COLORTERM': 'truecolor', 'GIO_LAUNCHED_DESKTOP_FILE_PID': '624006', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'IM_CONFIG_PHASE': '1', 'WAYLAND_DISPLAY': 'wayland-0', 'COLCON_PREFIX_PATH': '/home/<USER>/ros2_ws/install:/home/<USER>/dev_ws/install', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'ling', 'JOURNAL_STREAM': '9:9708', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'MEMORY_PRESSURE_WATCH': '/sys/fs/cgroup/user.slice/user-1000.slice/<EMAIL>/session.slice/<EMAIL>/memory.pressure', 'XDG_SESSION_CLASS': 'user', 'USERNAME': 'ling', 'TERM': 'xterm-256color', 'GNOME_DESKTOP_SESSION_ID': 'this-is-deprecated', 'PATH': '/home/<USER>/.local/bin:/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.local/bin:/home/<USER>/.local/bin:/home/<USER>/.local/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.local/bin:/home/<USER>/.local/bin', 'SESSION_MANAGER': 'local/ling-Legion-R9000P-ARX8:@/tmp/.ICE-unix/383882,unix/ling-Legion-R9000P-ARX8:/tmp/.ICE-unix/383882', 'INVOCATION_ID': '2a5514af82624541951b7a5f91d3aec5', 'PAPERSIZE': 'letter', 'XDG_MENU_PREFIX': 'gnome-', 'LC_ADDRESS': 'en_US.UTF-8', 'GNOME_SETUP_DISPLAY': ':1', 'XDG_RUNTIME_DIR': '/run/user/1000', 'GDK_BACKEND': 'x11', 'DISPLAY': ':0', 'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS': '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-292cc37d3dd9c4c3.txt', 'LANG': 'zh_CN.UTF-8', 'XDG_CURRENT_DESKTOP': 'Unity', 'PYTHONSTARTUP': '/home/<USER>/.config/Code/User/workspaceStorage/3d2483499d9f68c8ed0784230a342deb/ms-python.python/pythonrc.py', 'LC_TELEPHONE': 'en_US.UTF-8', 'XMODIFIERS': '@im=ibus', 'XDG_SESSION_DESKTOP': 'ubuntu', 'XAUTHORITY': '/run/user/1000/.mutter-Xwaylandauth.34LDD3', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'VSCODE_GIT_IPC_HANDLE': '/run/user/1000/vscode-git-3bc09f8ddd.sock', 'TERM_PROGRAM': 'vscode', 'SSH_AUTH_SOCK': '/run/user/1000/keyring/ssh', 'AMENT_PREFIX_PATH': '/home/<USER>/ros2_ws/install/tide_msgs:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic_cpp:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf_cpp:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service_cpp:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_pkg_c:/home/<USER>/dev_ws/install/learning_parameter_cpp:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node_cpp:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action_cpp:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_interface:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'ubuntu:GNOME', 'SHELL': '/usr/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'QT_ACCESSIBILITY': '1', 'PROMPT_COMMAND': 'echo "$(pwd)" > \'/tmp/augment-cwd-82e1c39c3a1086f8.txt\'\n__augment_output_end_marker', 'GDMSESSION': 'ubuntu', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'GJS_DEBUG_OUTPUT': 'stderr', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'GIT_PAGER': 'cat', 'QT_IM_MODULE': 'ibus', 'PWD': '/home/<USER>/robot_1/build/pkg_gazebo', 'LC_ALL': 'en_US.UTF-8', 'XDG_CONFIG_DIRS': '/etc/xdg/xdg-ubuntu:/etc/xdg', 'XDG_DATA_DIRS': '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/ros2_ws/install/tide_msgs/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_urdf/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_topic/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_tf/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_service/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_qos/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_pkg_python/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_parameter/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_node/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_launch/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_action/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_interface/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_gazebo_harmonic/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_gazebo/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_cv/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'MEMORY_PRESSURE_WRITE': 'c29tZSAyMDAwMDAgMjAwMDAwMAA=', 'CMAKE_PREFIX_PATH': '/home/<USER>/ros2_ws/install/tide_msgs:/home/<USER>/dev_ws/install/learning_topic_cpp:/home/<USER>/dev_ws/install/learning_tf_cpp:/home/<USER>/dev_ws/install/learning_service_cpp:/home/<USER>/dev_ws/install/learning_pkg_c:/home/<USER>/dev_ws/install/learning_parameter_cpp:/home/<USER>/dev_ws/install/learning_node_cpp:/home/<USER>/dev_ws/install/learning_action_cpp:/home/<USER>/dev_ws/install/learning_interface:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy'}), 'shell': False}
[0.029372] (pkg_gazebo) StdoutLine: {'line': b'-- Found ament_cmake: 2.5.4 (/opt/ros/jazzy/share/ament_cmake/cmake)\n'}
[0.099740] (-) TimerEvent: {}
[0.184567] (pkg_gazebo) StdoutLine: {'line': b'-- Override CMake install command with custom implementation using symlinks instead of copying resources\n'}
[0.195901] (pkg_gazebo) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.17.2 (/opt/ros/jazzy/share/ament_lint_auto/cmake)\n'}
[0.199823] (-) TimerEvent: {}
[0.263781] (pkg_gazebo) StdoutLine: {'line': b"-- Added test 'flake8' to check Python code syntax and style conventions\n"}
[0.263980] (pkg_gazebo) StdoutLine: {'line': b"-- Configured 'flake8' exclude dirs and/or files: \n"}
[0.264275] (pkg_gazebo) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[0.264741] (pkg_gazebo) StdoutLine: {'line': b"-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257\n"}
[0.266583] (pkg_gazebo) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[0.267550] (pkg_gazebo) StdoutLine: {'line': b'-- Configuring done (0.2s)\n'}
[0.270622] (pkg_gazebo) StdoutLine: {'line': b'-- Generating done (0.0s)\n'}
[0.271945] (pkg_gazebo) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/robot_1/build/pkg_gazebo\n'}
[0.293703] (pkg_gazebo) CommandEnded: {'returncode': 0}
[0.294405] (pkg_gazebo) JobProgress: {'identifier': 'pkg_gazebo', 'progress': 'install'}
[0.299930] (-) TimerEvent: {}
[0.303883] (pkg_gazebo) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/robot_1/build/pkg_gazebo'], 'cwd': '/home/<USER>/robot_1/build/pkg_gazebo', 'env': OrderedDict({'GJS_DEBUG_TOPICS': 'JS ERROR;JS LOG', 'PYTHON_BASIC_REPL': '1', 'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'zh_CN:zh_HK:en', 'USER': 'ling', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'wayland', 'GIT_ASKPASS': '/usr/share/code/resources/app/extensions/git/dist/askpass.sh', 'CLUTTER_DISABLE_MIPMAPPED_TEXT': '1', 'SHLVL': '2', 'LD_LIBRARY_PATH': '/home/<USER>/ros2_ws/install/tide_msgs/lib:/home/<USER>/dev_ws/install/learning_interface/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'LESS': '-FX', 'HOME': '/home/<USER>', 'CHROME_DESKTOP': 'code.desktop', 'TERM_PROGRAM_VERSION': '1.101.0', 'DESKTOP_SESSION': 'ubuntu', 'GIO_LAUNCHED_DESKTOP_FILE': '/usr/share/applications/code.desktop', 'ROS_PYTHON_VERSION': '3', 'GNOME_SHELL_SESSION_MODE': 'ubuntu', 'GTK_MODULES': 'gail:atk-bridge', 'PAGER': 'cat', 'VSCODE_GIT_ASKPASS_MAIN': '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js', 'LC_MONETARY': 'en_US.UTF-8', 'VSCODE_GIT_ASKPASS_NODE': '/usr/share/code/code', 'MANAGERPID': '383655', 'PYDEVD_DISABLE_FILE_VALIDATION': '1', 'SYSTEMD_EXEC_PID': '383931', 'BUNDLED_DEBUGPY_PATH': '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy', 'IM_CONFIG_CHECK_ENV': '1', 'GSM_SKIP_SSH_AGENT_WORKAROUND': 'true', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'COLORTERM': 'truecolor', 'GIO_LAUNCHED_DESKTOP_FILE_PID': '624006', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'IM_CONFIG_PHASE': '1', 'WAYLAND_DISPLAY': 'wayland-0', 'COLCON_PREFIX_PATH': '/home/<USER>/ros2_ws/install:/home/<USER>/dev_ws/install', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'ling', 'JOURNAL_STREAM': '9:9708', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'MEMORY_PRESSURE_WATCH': '/sys/fs/cgroup/user.slice/user-1000.slice/<EMAIL>/session.slice/<EMAIL>/memory.pressure', 'XDG_SESSION_CLASS': 'user', 'USERNAME': 'ling', 'TERM': 'xterm-256color', 'GNOME_DESKTOP_SESSION_ID': 'this-is-deprecated', 'PATH': '/home/<USER>/.local/bin:/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.local/bin:/home/<USER>/.local/bin:/home/<USER>/.local/bin:/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.local/bin:/home/<USER>/.local/bin', 'SESSION_MANAGER': 'local/ling-Legion-R9000P-ARX8:@/tmp/.ICE-unix/383882,unix/ling-Legion-R9000P-ARX8:/tmp/.ICE-unix/383882', 'INVOCATION_ID': '2a5514af82624541951b7a5f91d3aec5', 'PAPERSIZE': 'letter', 'XDG_MENU_PREFIX': 'gnome-', 'LC_ADDRESS': 'en_US.UTF-8', 'GNOME_SETUP_DISPLAY': ':1', 'XDG_RUNTIME_DIR': '/run/user/1000', 'GDK_BACKEND': 'x11', 'DISPLAY': ':0', 'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS': '/home/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-292cc37d3dd9c4c3.txt', 'LANG': 'zh_CN.UTF-8', 'XDG_CURRENT_DESKTOP': 'Unity', 'PYTHONSTARTUP': '/home/<USER>/.config/Code/User/workspaceStorage/3d2483499d9f68c8ed0784230a342deb/ms-python.python/pythonrc.py', 'LC_TELEPHONE': 'en_US.UTF-8', 'XMODIFIERS': '@im=ibus', 'XDG_SESSION_DESKTOP': 'ubuntu', 'XAUTHORITY': '/run/user/1000/.mutter-Xwaylandauth.34LDD3', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'VSCODE_GIT_IPC_HANDLE': '/run/user/1000/vscode-git-3bc09f8ddd.sock', 'TERM_PROGRAM': 'vscode', 'SSH_AUTH_SOCK': '/run/user/1000/keyring/ssh', 'AMENT_PREFIX_PATH': '/home/<USER>/ros2_ws/install/tide_msgs:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic_cpp:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf_cpp:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service_cpp:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_pkg_c:/home/<USER>/dev_ws/install/learning_parameter_cpp:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node_cpp:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action_cpp:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_interface:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'ubuntu:GNOME', 'SHELL': '/usr/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'QT_ACCESSIBILITY': '1', 'PROMPT_COMMAND': 'echo "$(pwd)" > \'/tmp/augment-cwd-82e1c39c3a1086f8.txt\'\n__augment_output_end_marker', 'GDMSESSION': 'ubuntu', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'GJS_DEBUG_OUTPUT': 'stderr', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'GIT_PAGER': 'cat', 'QT_IM_MODULE': 'ibus', 'PWD': '/home/<USER>/robot_1/build/pkg_gazebo', 'LC_ALL': 'en_US.UTF-8', 'XDG_CONFIG_DIRS': '/etc/xdg/xdg-ubuntu:/etc/xdg', 'XDG_DATA_DIRS': '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/ros2_ws/install/tide_msgs/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_urdf/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_topic/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_tf/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_service/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_qos/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_pkg_python/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_parameter/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_node/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_launch/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_action/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_interface/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_gazebo_harmonic/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_gazebo/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_cv/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'MEMORY_PRESSURE_WRITE': 'c29tZSAyMDAwMDAgMjAwMDAwMAA=', 'CMAKE_PREFIX_PATH': '/home/<USER>/ros2_ws/install/tide_msgs:/home/<USER>/dev_ws/install/learning_topic_cpp:/home/<USER>/dev_ws/install/learning_tf_cpp:/home/<USER>/dev_ws/install/learning_service_cpp:/home/<USER>/dev_ws/install/learning_pkg_c:/home/<USER>/dev_ws/install/learning_parameter_cpp:/home/<USER>/dev_ws/install/learning_node_cpp:/home/<USER>/dev_ws/install/learning_action_cpp:/home/<USER>/dev_ws/install/learning_interface:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy'}), 'shell': False}
[0.311679] (pkg_gazebo) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.311904] (pkg_gazebo) StdoutLine: {'line': b'-- Execute custom install script\n'}
[0.312023] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//launch/display.launch.py\n'}
[0.312126] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//launch/gazebo.launch.py\n'}
[0.312253] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//urdf/urbubing.urdf\n'}
[0.312348] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//urdf/urbubing.xacro\n'}
[0.312450] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//urdf/urbubing_gazebo.xacro\n'}
[0.312589] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//urdf/urbubing_macros.xacro\n'}
[0.312783] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/base_link.STL\n'}
[0.312907] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/dials_Link.STL\n'}
[0.312983] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/pitch_Link.STL\n'}
[0.313042] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/pitch_aid_Link1.STL\n'}
[0.313096] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/pitch_aid_Link2.STL\n'}
[0.313150] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/pitch_aid_Link3.STL\n'}
[0.313249] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/wheel_lb_Link.STL\n'}
[0.313304] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/wheel_lf_Link.STL\n'}
[0.313357] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/wheel_rb_Link.STL\n'}
[0.313412] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/wheel_rf_Link.STL\n'}
[0.313469] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/yaw_Link.STL\n'}
[0.313551] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//config/joint_names_urbubing.yaml\n'}
[0.313625] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//config/rviz/display_model.py\n'}
[0.313717] (pkg_gazebo) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//worlds/test_world.world\n'}
[0.322478] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/ament_index/resource_index/package_run_dependencies/pkg_gazebo\n'}
[0.322666] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/ament_index/resource_index/parent_prefix_path/pkg_gazebo\n'}
[0.322749] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/environment/ament_prefix_path.sh\n'}
[0.322811] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/environment/ament_prefix_path.dsv\n'}
[0.322904] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/environment/path.sh\n'}
[0.322965] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/environment/path.dsv\n'}
[0.323038] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/local_setup.bash\n'}
[0.323173] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/local_setup.sh\n'}
[0.323353] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/local_setup.zsh\n'}
[0.323464] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/local_setup.dsv\n'}
[0.323536] (pkg_gazebo) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/package.dsv\n'}
[0.331939] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/ament_index/resource_index/packages/pkg_gazebo\n'}
[0.332113] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/cmake/pkg_gazeboConfig.cmake\n'}
[0.332200] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/cmake/pkg_gazeboConfig-version.cmake\n'}
[0.332258] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/package.xml\n'}
[0.333814] (pkg_gazebo) CommandEnded: {'returncode': 0}
[0.343799] (pkg_gazebo) JobEnded: {'identifier': 'pkg_gazebo', 'rc': 0}
[0.344434] (-) EventReactorShutdown: {}
