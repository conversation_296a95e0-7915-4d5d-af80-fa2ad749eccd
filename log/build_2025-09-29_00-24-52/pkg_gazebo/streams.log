[0.008s] Invoking command in '/home/<USER>/robot_1/build/pkg_gazebo': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/robot_1/build/pkg_gazebo -- -j32 -l32
[0.029s] -- Found ament_cmake: 2.5.4 (/opt/ros/jazzy/share/ament_cmake/cmake)
[0.184s] -- Override CMake install command with custom implementation using symlinks instead of copying resources
[0.195s] -- Found ament_lint_auto: 0.17.2 (/opt/ros/jazzy/share/ament_lint_auto/cmake)
[0.263s] -- Added test 'flake8' to check Python code syntax and style conventions
[0.263s] -- Configured 'flake8' exclude dirs and/or files: 
[0.264s] -- Added test 'lint_cmake' to check CMake code style
[0.264s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[0.266s] -- Added test 'xmllint' to check XML markup files
[0.267s] -- Configuring done (0.2s)
[0.270s] -- Generating done (0.0s)
[0.271s] -- Build files have been written to: /home/<USER>/robot_1/build/pkg_gazebo
[0.294s] Invoked command in '/home/<USER>/robot_1/build/pkg_gazebo' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/robot_1/build/pkg_gazebo -- -j32 -l32
[0.304s] Invoking command in '/home/<USER>/robot_1/build/pkg_gazebo': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/robot_1/build/pkg_gazebo
[0.311s] -- Install configuration: ""
[0.311s] -- Execute custom install script
[0.312s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//launch/display.launch.py
[0.312s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//launch/gazebo.launch.py
[0.312s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//urdf/urbubing.urdf
[0.312s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//urdf/urbubing.xacro
[0.312s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//urdf/urbubing_gazebo.xacro
[0.312s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//urdf/urbubing_macros.xacro
[0.312s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/base_link.STL
[0.312s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/dials_Link.STL
[0.312s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/pitch_Link.STL
[0.313s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/pitch_aid_Link1.STL
[0.313s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/pitch_aid_Link2.STL
[0.313s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/pitch_aid_Link3.STL
[0.313s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/wheel_lb_Link.STL
[0.313s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/wheel_lf_Link.STL
[0.313s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/wheel_rb_Link.STL
[0.313s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/wheel_rf_Link.STL
[0.313s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/yaw_Link.STL
[0.313s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//config/joint_names_urbubing.yaml
[0.313s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//config/rviz/display_model.py
[0.313s] -- Symlinking: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//worlds/test_world.world
[0.322s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/ament_index/resource_index/package_run_dependencies/pkg_gazebo
[0.322s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/ament_index/resource_index/parent_prefix_path/pkg_gazebo
[0.322s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/environment/ament_prefix_path.sh
[0.322s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/environment/ament_prefix_path.dsv
[0.322s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/environment/path.sh
[0.322s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/environment/path.dsv
[0.323s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/local_setup.bash
[0.323s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/local_setup.sh
[0.323s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/local_setup.zsh
[0.323s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/local_setup.dsv
[0.323s] -- Symlinking: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/package.dsv
[0.331s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/ament_index/resource_index/packages/pkg_gazebo
[0.332s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/cmake/pkg_gazeboConfig.cmake
[0.332s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/cmake/pkg_gazeboConfig-version.cmake
[0.332s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/package.xml
[0.333s] Invoked command in '/home/<USER>/robot_1/build/pkg_gazebo' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/robot_1/build/pkg_gazebo
