-- Install configuration: ""
-- Execute custom install script
-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//launch/display.launch.py
-- Symlinking: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//launch/gazebo_harmonic.launch.py
-- Symlinking: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//launch/gazebo_test.launch.py
-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//launch/gz_test.launch.py
-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//launch/ignition_gazebo.launch.py
-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//urdf/urbubing.urdf
-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//urdf/urbubing.xacro
-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//urdf/urbubing_macros.xacro
-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/base_link.STL
-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/dials_Link.STL
-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/pitch_Link.STL
-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/pitch_aid_Link1.STL
-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/pitch_aid_Link2.STL
-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/pitch_aid_Link3.STL
-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/wheel_lb_Link.STL
-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/wheel_lf_Link.STL
-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/wheel_rb_Link.STL
-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/wheel_rf_Link.STL
-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/yaw_Link.STL
-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//config/joint_names_urbubing.yaml
-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//config/rviz/display_model.py
-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/ament_index/resource_index/package_run_dependencies/pkg_gazebo
-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/ament_index/resource_index/parent_prefix_path/pkg_gazebo
-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/environment/ament_prefix_path.sh
-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/environment/ament_prefix_path.dsv
-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/environment/path.sh
-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/environment/path.dsv
-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/local_setup.bash
-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/local_setup.sh
-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/local_setup.zsh
-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/local_setup.dsv
-- Symlinking: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/package.dsv
-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/ament_index/resource_index/packages/pkg_gazebo
-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/cmake/pkg_gazeboConfig.cmake
-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/cmake/pkg_gazeboConfig-version.cmake
-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/package.xml
