[0.087s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.087s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=32, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x7a6adb8b2f00>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7a6adb8b2a20>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7a6adb8b2a20>>, mixin_verb=('build',))
[0.109s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.109s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.109s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.109s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.109s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.109s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.109s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/robot_1'
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.123s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.123s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg_gazebo) by extensions ['ignore', 'ignore_ament_install']
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg_gazebo) by extension 'ignore'
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg_gazebo) by extension 'ignore_ament_install'
[0.125s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg_gazebo) by extensions ['colcon_pkg']
[0.125s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg_gazebo) by extension 'colcon_pkg'
[0.125s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg_gazebo) by extensions ['colcon_meta']
[0.125s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg_gazebo) by extension 'colcon_meta'
[0.125s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg_gazebo) by extensions ['ros']
[0.125s] Level 1:colcon.colcon_core.package_identification:_identify(src/pkg_gazebo) by extension 'ros'
[0.126s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pkg_gazebo' with type 'ros.ament_cmake' and name 'pkg_gazebo'
[0.126s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.126s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.126s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.126s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.126s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.136s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.136s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.137s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 1 installed packages in /home/<USER>/ros2_ws/install
[0.137s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 21 installed packages in /home/<USER>/dev_ws/install
[0.139s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 375 installed packages in /opt/ros/jazzy
[0.140s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.169s] Level 5:colcon.colcon_core.verb:set package 'pkg_gazebo' build argument 'cmake_args' from command line to 'None'
[0.169s] Level 5:colcon.colcon_core.verb:set package 'pkg_gazebo' build argument 'cmake_target' from command line to 'None'
[0.169s] Level 5:colcon.colcon_core.verb:set package 'pkg_gazebo' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.169s] Level 5:colcon.colcon_core.verb:set package 'pkg_gazebo' build argument 'cmake_clean_cache' from command line to 'False'
[0.169s] Level 5:colcon.colcon_core.verb:set package 'pkg_gazebo' build argument 'cmake_clean_first' from command line to 'False'
[0.169s] Level 5:colcon.colcon_core.verb:set package 'pkg_gazebo' build argument 'cmake_force_configure' from command line to 'False'
[0.170s] Level 5:colcon.colcon_core.verb:set package 'pkg_gazebo' build argument 'ament_cmake_args' from command line to 'None'
[0.170s] Level 5:colcon.colcon_core.verb:set package 'pkg_gazebo' build argument 'catkin_cmake_args' from command line to 'None'
[0.170s] Level 5:colcon.colcon_core.verb:set package 'pkg_gazebo' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.170s] DEBUG:colcon.colcon_core.verb:Building package 'pkg_gazebo' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/robot_1/build/pkg_gazebo', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/robot_1/install/pkg_gazebo', 'merge_install': False, 'path': '/home/<USER>/robot_1/src/pkg_gazebo', 'symlink_install': False, 'test_result_base': None}
[0.170s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.170s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.171s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/robot_1/src/pkg_gazebo' with build type 'ament_cmake'
[0.171s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/robot_1/src/pkg_gazebo'
[0.172s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.172s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.172s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.180s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/robot_1/build/pkg_gazebo': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/robot_1/build/pkg_gazebo -- -j32 -l32
[0.216s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/robot_1/build/pkg_gazebo' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/robot_1/build/pkg_gazebo -- -j32 -l32
[0.223s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/robot_1/build/pkg_gazebo': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/robot_1/build/pkg_gazebo
[0.255s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(pkg_gazebo)
[0.255s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/robot_1/build/pkg_gazebo' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/robot_1/build/pkg_gazebo
[0.256s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot_1/install/pkg_gazebo' for CMake module files
[0.256s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot_1/install/pkg_gazebo' for CMake config files
[0.257s] Level 1:colcon.colcon_core.shell:create_environment_hook('pkg_gazebo', 'cmake_prefix_path')
[0.257s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/hook/cmake_prefix_path.ps1'
[0.257s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/hook/cmake_prefix_path.dsv'
[0.257s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/hook/cmake_prefix_path.sh'
[0.258s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot_1/install/pkg_gazebo/bin'
[0.258s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot_1/install/pkg_gazebo/lib/pkgconfig/pkg_gazebo.pc'
[0.258s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot_1/install/pkg_gazebo/lib/python3.12/site-packages'
[0.258s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot_1/install/pkg_gazebo/bin'
[0.258s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/package.ps1'
[0.259s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/package.dsv'
[0.259s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/package.sh'
[0.259s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/package.bash'
[0.260s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/package.zsh'
[0.260s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/robot_1/install/pkg_gazebo/share/colcon-core/packages/pkg_gazebo)
[0.260s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(pkg_gazebo)
[0.260s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot_1/install/pkg_gazebo' for CMake module files
[0.260s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot_1/install/pkg_gazebo' for CMake config files
[0.261s] Level 1:colcon.colcon_core.shell:create_environment_hook('pkg_gazebo', 'cmake_prefix_path')
[0.261s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/hook/cmake_prefix_path.ps1'
[0.261s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/hook/cmake_prefix_path.dsv'
[0.261s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/hook/cmake_prefix_path.sh'
[0.261s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot_1/install/pkg_gazebo/bin'
[0.261s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot_1/install/pkg_gazebo/lib/pkgconfig/pkg_gazebo.pc'
[0.262s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot_1/install/pkg_gazebo/lib/python3.12/site-packages'
[0.262s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/robot_1/install/pkg_gazebo/bin'
[0.262s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/package.ps1'
[0.262s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/package.dsv'
[0.262s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/package.sh'
[0.263s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/package.bash'
[0.263s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/package.zsh'
[0.263s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/robot_1/install/pkg_gazebo/share/colcon-core/packages/pkg_gazebo)
[0.263s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[0.263s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[0.263s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[0.263s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[0.267s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[0.267s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[0.267s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[0.274s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[0.274s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/robot_1/install/local_setup.ps1'
[0.274s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/robot_1/install/_local_setup_util_ps1.py'
[0.275s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/robot_1/install/setup.ps1'
[0.276s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/robot_1/install/local_setup.sh'
[0.276s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/robot_1/install/_local_setup_util_sh.py'
[0.277s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/robot_1/install/setup.sh'
[0.278s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/robot_1/install/local_setup.bash'
[0.278s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/robot_1/install/setup.bash'
[0.279s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/robot_1/install/local_setup.zsh'
[0.279s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/robot_1/install/setup.zsh'
