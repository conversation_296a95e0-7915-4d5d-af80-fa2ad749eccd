[0.000000] (-) TimerEvent: {}
[0.000258] (pkg_gazebo) JobQueued: {'identifier': 'pkg_gazebo', 'dependencies': OrderedDict()}
[0.000366] (pkg_gazebo) JobStarted: {'identifier': 'pkg_gazebo'}
[0.006977] (pkg_gazebo) JobProgress: {'identifier': 'pkg_gazebo', 'progress': 'cmake'}
[0.007344] (pkg_gazebo) JobProgress: {'identifier': 'pkg_gazebo', 'progress': 'build'}
[0.007810] (pkg_gazebo) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/robot_1/build/pkg_gazebo', '--', '-j32', '-l32'], 'cwd': '/home/<USER>/robot_1/build/pkg_gazebo', 'env': OrderedDict({'GJS_DEBUG_TOPICS': 'JS ERROR;JS LOG', 'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'zh_CN:zh_HK:en', 'USER': 'ling', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'wayland', 'GIT_ASKPASS': '/usr/share/cursor/resources/app/extensions/git/dist/askpass.sh', 'CLUTTER_DISABLE_MIPMAPPED_TEXT': '1', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/home/<USER>/ros2_ws/install/tide_msgs/lib:/home/<USER>/dev_ws/install/learning_interface/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'CHROME_DESKTOP': 'cursor.desktop', 'TERM_PROGRAM_VERSION': '1.5.5', 'DESKTOP_SESSION': 'ubuntu', 'GIO_LAUNCHED_DESKTOP_FILE': '/usr/share/applications/cursor.desktop', 'ROS_PYTHON_VERSION': '3', 'GNOME_SHELL_SESSION_MODE': 'ubuntu', 'GTK_MODULES': 'gail:atk-bridge', 'VSCODE_GIT_ASKPASS_MAIN': '/usr/share/cursor/resources/app/extensions/git/dist/askpass-main.js', 'LC_MONETARY': 'en_US.UTF-8', 'VSCODE_GIT_ASKPASS_NODE': '/usr/share/cursor/cursor', 'MANAGERPID': '383655', 'PYDEVD_DISABLE_FILE_VALIDATION': '1', 'SYSTEMD_EXEC_PID': '383931', 'BUNDLED_DEBUGPY_PATH': '/home/<USER>/.cursor/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy', 'IM_CONFIG_CHECK_ENV': '1', 'GSM_SKIP_SSH_AGENT_WORKAROUND': 'true', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'COLORTERM': 'truecolor', 'GIO_LAUNCHED_DESKTOP_FILE_PID': '642436', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'IM_CONFIG_PHASE': '1', 'WAYLAND_DISPLAY': 'wayland-0', 'COLCON_PREFIX_PATH': '/home/<USER>/ros2_ws/install:/home/<USER>/dev_ws/install', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'ling', 'JOURNAL_STREAM': '9:9708', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'MEMORY_PRESSURE_WATCH': '/sys/fs/cgroup/user.slice/user-1000.slice/<EMAIL>/session.slice/<EMAIL>/memory.pressure', 'XDG_SESSION_CLASS': 'user', 'USERNAME': 'ling', 'TERM': 'xterm-256color', 'GNOME_DESKTOP_SESSION_ID': 'this-is-deprecated', 'PATH': '/home/<USER>/.local/bin:/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.local/bin:/home/<USER>/.local/bin:/home/<USER>/.local/bin:/home/<USER>/.cursor/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.local/bin', 'SESSION_MANAGER': 'local/ling-Legion-R9000P-ARX8:@/tmp/.ICE-unix/383882,unix/ling-Legion-R9000P-ARX8:/tmp/.ICE-unix/383882', 'INVOCATION_ID': '2a5514af82624541951b7a5f91d3aec5', 'PAPERSIZE': 'letter', 'XDG_MENU_PREFIX': 'gnome-', 'LC_ADDRESS': 'en_US.UTF-8', 'GNOME_SETUP_DISPLAY': ':1', 'XDG_RUNTIME_DIR': '/run/user/1000', 'GDK_BACKEND': 'x11', 'DISPLAY': ':0', 'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS': '/home/<USER>/.cursor/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-292cc37d3dd9c4c3.txt', 'LANG': 'zh_CN.UTF-8', 'XDG_CURRENT_DESKTOP': 'Unity', 'LC_TELEPHONE': 'en_US.UTF-8', 'XMODIFIERS': '@im=ibus', 'XDG_SESSION_DESKTOP': 'ubuntu', 'XAUTHORITY': '/run/user/1000/.mutter-Xwaylandauth.34LDD3', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'SBX_CHROME_API_RQ': '1', 'VSCODE_GIT_IPC_HANDLE': '/run/user/1000/vscode-git-e6c7273bcd.sock', 'TERM_PROGRAM': 'vscode', 'CURSOR_TRACE_ID': '89ce3e006e844ecea4aea7cda3d71494', 'SSH_AUTH_SOCK': '/run/user/1000/keyring/ssh', 'AMENT_PREFIX_PATH': '/home/<USER>/ros2_ws/install/tide_msgs:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic_cpp:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf_cpp:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service_cpp:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_pkg_c:/home/<USER>/dev_ws/install/learning_parameter_cpp:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node_cpp:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action_cpp:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_interface:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'ubuntu:GNOME', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'QT_ACCESSIBILITY': '1', 'GDMSESSION': 'ubuntu', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'GJS_DEBUG_OUTPUT': 'stderr', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'QT_IM_MODULE': 'ibus', 'PWD': '/home/<USER>/robot_1/build/pkg_gazebo', 'LC_ALL': 'en_US.UTF-8', 'XDG_CONFIG_DIRS': '/etc/xdg/xdg-ubuntu:/etc/xdg', 'XDG_DATA_DIRS': '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/ros2_ws/install/tide_msgs/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_urdf/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_topic/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_tf/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_service/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_qos/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_pkg_python/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_parameter/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_node/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_launch/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_action/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_interface/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_gazebo_harmonic/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_gazebo/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_cv/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'MEMORY_PRESSURE_WRITE': 'c29tZSAyMDAwMDAgMjAwMDAwMAA=', 'CMAKE_PREFIX_PATH': '/home/<USER>/ros2_ws/install/tide_msgs:/home/<USER>/dev_ws/install/learning_topic_cpp:/home/<USER>/dev_ws/install/learning_tf_cpp:/home/<USER>/dev_ws/install/learning_service_cpp:/home/<USER>/dev_ws/install/learning_pkg_c:/home/<USER>/dev_ws/install/learning_parameter_cpp:/home/<USER>/dev_ws/install/learning_node_cpp:/home/<USER>/dev_ws/install/learning_action_cpp:/home/<USER>/dev_ws/install/learning_interface:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy'}), 'shell': False}
[0.045084] (pkg_gazebo) CommandEnded: {'returncode': 0}
[0.045514] (pkg_gazebo) JobProgress: {'identifier': 'pkg_gazebo', 'progress': 'install'}
[0.052651] (pkg_gazebo) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/robot_1/build/pkg_gazebo'], 'cwd': '/home/<USER>/robot_1/build/pkg_gazebo', 'env': OrderedDict({'GJS_DEBUG_TOPICS': 'JS ERROR;JS LOG', 'LESSOPEN': '| /usr/bin/lesspipe %s', 'LANGUAGE': 'zh_CN:zh_HK:en', 'USER': 'ling', 'LC_TIME': 'en_US.UTF-8', 'GZ_CONFIG_PATH': '/opt/ros/jazzy/opt/gz_sim_vendor/share/gz:/opt/ros/jazzy/opt/sdformat_vendor/share/gz:/opt/ros/jazzy/opt/gz_gui_vendor/share/gz:/opt/ros/jazzy/opt/gz_transport_vendor/share/gz:/opt/ros/jazzy/opt/gz_rendering_vendor/share/gz:/opt/ros/jazzy/opt/gz_plugin_vendor/share/gz:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/share/gz:/opt/ros/jazzy/opt/gz_msgs_vendor/share/gz:/opt/ros/jazzy/opt/gz_common_vendor/share/gz', 'XDG_SESSION_TYPE': 'wayland', 'GIT_ASKPASS': '/usr/share/cursor/resources/app/extensions/git/dist/askpass.sh', 'CLUTTER_DISABLE_MIPMAPPED_TEXT': '1', 'SHLVL': '1', 'LD_LIBRARY_PATH': '/home/<USER>/ros2_ws/install/tide_msgs/lib:/home/<USER>/dev_ws/install/learning_interface/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib', 'HOME': '/home/<USER>', 'CHROME_DESKTOP': 'cursor.desktop', 'TERM_PROGRAM_VERSION': '1.5.5', 'DESKTOP_SESSION': 'ubuntu', 'GIO_LAUNCHED_DESKTOP_FILE': '/usr/share/applications/cursor.desktop', 'ROS_PYTHON_VERSION': '3', 'GNOME_SHELL_SESSION_MODE': 'ubuntu', 'GTK_MODULES': 'gail:atk-bridge', 'VSCODE_GIT_ASKPASS_MAIN': '/usr/share/cursor/resources/app/extensions/git/dist/askpass-main.js', 'LC_MONETARY': 'en_US.UTF-8', 'VSCODE_GIT_ASKPASS_NODE': '/usr/share/cursor/cursor', 'MANAGERPID': '383655', 'PYDEVD_DISABLE_FILE_VALIDATION': '1', 'SYSTEMD_EXEC_PID': '383931', 'BUNDLED_DEBUGPY_PATH': '/home/<USER>/.cursor/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/libs/debugpy', 'IM_CONFIG_CHECK_ENV': '1', 'GSM_SKIP_SSH_AGENT_WORKAROUND': 'true', 'DBUS_SESSION_BUS_ADDRESS': 'unix:path=/run/user/1000/bus', 'COLORTERM': 'truecolor', 'GIO_LAUNCHED_DESKTOP_FILE_PID': '642436', 'DEBUGINFOD_URLS': 'https://debuginfod.ubuntu.com', 'IM_CONFIG_PHASE': '1', 'WAYLAND_DISPLAY': 'wayland-0', 'COLCON_PREFIX_PATH': '/home/<USER>/ros2_ws/install:/home/<USER>/dev_ws/install', 'ROS_DISTRO': 'jazzy', 'LOGNAME': 'ling', 'JOURNAL_STREAM': '9:9708', '_': '/usr/bin/colcon', 'ROS_VERSION': '2', 'MEMORY_PRESSURE_WATCH': '/sys/fs/cgroup/user.slice/user-1000.slice/<EMAIL>/session.slice/<EMAIL>/memory.pressure', 'XDG_SESSION_CLASS': 'user', 'USERNAME': 'ling', 'TERM': 'xterm-256color', 'GNOME_DESKTOP_SESSION_ID': 'this-is-deprecated', 'PATH': '/home/<USER>/.local/bin:/opt/ros/jazzy/opt/gz_msgs_vendor/bin:/opt/ros/jazzy/opt/gz_tools_vendor/bin:/opt/ros/jazzy/opt/gz_ogre_next_vendor/bin:/opt/ros/jazzy/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin:/home/<USER>/.local/bin:/home/<USER>/.local/bin:/home/<USER>/.local/bin:/home/<USER>/.cursor/extensions/ms-python.debugpy-2025.10.0-linux-x64/bundled/scripts/noConfigScripts:/home/<USER>/.local/bin', 'SESSION_MANAGER': 'local/ling-Legion-R9000P-ARX8:@/tmp/.ICE-unix/383882,unix/ling-Legion-R9000P-ARX8:/tmp/.ICE-unix/383882', 'INVOCATION_ID': '2a5514af82624541951b7a5f91d3aec5', 'PAPERSIZE': 'letter', 'XDG_MENU_PREFIX': 'gnome-', 'LC_ADDRESS': 'en_US.UTF-8', 'GNOME_SETUP_DISPLAY': ':1', 'XDG_RUNTIME_DIR': '/run/user/1000', 'GDK_BACKEND': 'x11', 'DISPLAY': ':0', 'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS': '/home/<USER>/.cursor/extensions/ms-python.debugpy-2025.10.0-linux-x64/.noConfigDebugAdapterEndpoints/endpoint-292cc37d3dd9c4c3.txt', 'LANG': 'zh_CN.UTF-8', 'XDG_CURRENT_DESKTOP': 'Unity', 'LC_TELEPHONE': 'en_US.UTF-8', 'XMODIFIERS': '@im=ibus', 'XDG_SESSION_DESKTOP': 'ubuntu', 'XAUTHORITY': '/run/user/1000/.mutter-Xwaylandauth.34LDD3', 'LS_COLORS': 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=00:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.avif=01;35:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:*~=00;90:*#=00;90:*.bak=00;90:*.crdownload=00;90:*.dpkg-dist=00;90:*.dpkg-new=00;90:*.dpkg-old=00;90:*.dpkg-tmp=00;90:*.old=00;90:*.orig=00;90:*.part=00;90:*.rej=00;90:*.rpmnew=00;90:*.rpmorig=00;90:*.rpmsave=00;90:*.swp=00;90:*.tmp=00;90:*.ucf-dist=00;90:*.ucf-new=00;90:*.ucf-old=00;90:', 'SBX_CHROME_API_RQ': '1', 'VSCODE_GIT_IPC_HANDLE': '/run/user/1000/vscode-git-e6c7273bcd.sock', 'TERM_PROGRAM': 'vscode', 'CURSOR_TRACE_ID': '89ce3e006e844ecea4aea7cda3d71494', 'SSH_AUTH_SOCK': '/run/user/1000/keyring/ssh', 'AMENT_PREFIX_PATH': '/home/<USER>/ros2_ws/install/tide_msgs:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic_cpp:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf_cpp:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service_cpp:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_pkg_c:/home/<USER>/dev_ws/install/learning_parameter_cpp:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node_cpp:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action_cpp:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_interface:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy', 'ORIGINAL_XDG_CURRENT_DESKTOP': 'ubuntu:GNOME', 'SHELL': '/bin/bash', 'LC_NAME': 'en_US.UTF-8', 'QT_ACCESSIBILITY': '1', 'GDMSESSION': 'ubuntu', 'LESSCLOSE': '/usr/bin/lesspipe %s %s', 'IGN_GAZEBO_RESOURCE_PATH': '/opt/ros/jazzy/share', 'LC_MEASUREMENT': 'en_US.UTF-8', 'GJS_DEBUG_OUTPUT': 'stderr', 'LC_IDENTIFICATION': 'en_US.UTF-8', 'ROS_AUTOMATIC_DISCOVERY_RANGE': 'SUBNET', 'VSCODE_GIT_ASKPASS_EXTRA_ARGS': '', 'QT_IM_MODULE': 'ibus', 'PWD': '/home/<USER>/robot_1/build/pkg_gazebo', 'LC_ALL': 'en_US.UTF-8', 'XDG_CONFIG_DIRS': '/etc/xdg/xdg-ubuntu:/etc/xdg', 'XDG_DATA_DIRS': '/usr/share/ubuntu:/usr/share/gnome:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop', 'PYTHONPATH': '/home/<USER>/ros2_ws/install/tide_msgs/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_urdf/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_topic/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_tf/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_service/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_qos/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_pkg_python/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_parameter/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_node/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_launch/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_action/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_interface/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_gazebo_harmonic/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_gazebo/lib/python3.12/site-packages:/home/<USER>/dev_ws/install/learning_cv/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages', 'LC_NUMERIC': 'en_US.UTF-8', 'LC_PAPER': 'en_US.UTF-8', 'COLCON': '1', 'MEMORY_PRESSURE_WRITE': 'c29tZSAyMDAwMDAgMjAwMDAwMAA=', 'CMAKE_PREFIX_PATH': '/home/<USER>/ros2_ws/install/tide_msgs:/home/<USER>/dev_ws/install/learning_topic_cpp:/home/<USER>/dev_ws/install/learning_tf_cpp:/home/<USER>/dev_ws/install/learning_service_cpp:/home/<USER>/dev_ws/install/learning_pkg_c:/home/<USER>/dev_ws/install/learning_parameter_cpp:/home/<USER>/dev_ws/install/learning_node_cpp:/home/<USER>/dev_ws/install/learning_action_cpp:/home/<USER>/dev_ws/install/learning_interface:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy'}), 'shell': False}
[0.058395] (pkg_gazebo) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[0.058563] (pkg_gazebo) StdoutLine: {'line': b'-- Execute custom install script\n'}
[0.058793] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//launch/display.launch.py\n'}
[0.058881] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//launch/gazebo.launch.py\n'}
[0.058951] (pkg_gazebo) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//launch/urbubing_rviz.launch.py\n'}
[0.066080] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//urdf/urbubing.urdf\n'}
[0.066203] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//urdf/urbubing.xacro\n'}
[0.066273] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//urdf/urbubing_gazebo.xacro\n'}
[0.066326] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//urdf/urbubing_macros.xacro\n'}
[0.066434] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/base_link.STL\n'}
[0.066511] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/dials_Link.STL\n'}
[0.066593] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/pitch_Link.STL\n'}
[0.066678] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/pitch_aid_Link1.STL\n'}
[0.066738] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/pitch_aid_Link2.STL\n'}
[0.066815] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/pitch_aid_Link3.STL\n'}
[0.066872] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/wheel_lb_Link.STL\n'}
[0.066966] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/wheel_lf_Link.STL\n'}
[0.067039] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/wheel_rb_Link.STL\n'}
[0.067096] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/wheel_rf_Link.STL\n'}
[0.067149] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/yaw_Link.STL\n'}
[0.067212] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//config/joint_names_urbubing.yaml\n'}
[0.067297] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//config/rviz/display_model.py\n'}
[0.067352] (pkg_gazebo) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//config/rviz/urbubing_display.rviz\n'}
[0.074388] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//worlds/test_world.world\n'}
[0.074520] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/ament_index/resource_index/package_run_dependencies/pkg_gazebo\n'}
[0.074594] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/ament_index/resource_index/parent_prefix_path/pkg_gazebo\n'}
[0.074683] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/environment/ament_prefix_path.sh\n'}
[0.074751] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/environment/ament_prefix_path.dsv\n'}
[0.074835] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/environment/path.sh\n'}
[0.074918] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/environment/path.dsv\n'}
[0.075000] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/local_setup.bash\n'}
[0.075083] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/local_setup.sh\n'}
[0.075169] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/local_setup.zsh\n'}
[0.075245] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/local_setup.dsv\n'}
[0.075304] (pkg_gazebo) StdoutLine: {'line': b'-- Symlinking: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/package.dsv\n'}
[0.082311] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/ament_index/resource_index/packages/pkg_gazebo\n'}
[0.082431] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/cmake/pkg_gazeboConfig.cmake\n'}
[0.082513] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/cmake/pkg_gazeboConfig-version.cmake\n'}
[0.082610] (pkg_gazebo) StdoutLine: {'line': b'-- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/package.xml\n'}
[0.084188] (pkg_gazebo) CommandEnded: {'returncode': 0}
[0.092714] (pkg_gazebo) JobEnded: {'identifier': 'pkg_gazebo', 'rc': 0}
[0.093091] (-) EventReactorShutdown: {}
