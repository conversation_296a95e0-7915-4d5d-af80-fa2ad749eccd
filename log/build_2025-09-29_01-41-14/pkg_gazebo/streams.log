[0.009s] Invoking command in '/home/<USER>/robot_1/build/pkg_gazebo': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/robot_1/build/pkg_gazebo -- -j32 -l32
[0.045s] Invoked command in '/home/<USER>/robot_1/build/pkg_gazebo' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/robot_1/build/pkg_gazebo -- -j32 -l32
[0.052s] Invoking command in '/home/<USER>/robot_1/build/pkg_gazebo': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/robot_1/build/pkg_gazebo
[0.058s] -- Install configuration: ""
[0.058s] -- Execute custom install script
[0.058s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//launch/display.launch.py
[0.059s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//launch/gazebo.launch.py
[0.059s] -- Symlinking: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//launch/urbubing_rviz.launch.py
[0.066s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//urdf/urbubing.urdf
[0.066s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//urdf/urbubing.xacro
[0.066s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//urdf/urbubing_gazebo.xacro
[0.066s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//urdf/urbubing_macros.xacro
[0.066s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/base_link.STL
[0.066s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/dials_Link.STL
[0.066s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/pitch_Link.STL
[0.066s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/pitch_aid_Link1.STL
[0.066s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/pitch_aid_Link2.STL
[0.066s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/pitch_aid_Link3.STL
[0.067s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/wheel_lb_Link.STL
[0.067s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/wheel_lf_Link.STL
[0.067s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/wheel_rb_Link.STL
[0.067s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/wheel_rf_Link.STL
[0.067s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//meshes/yaw_Link.STL
[0.067s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//config/joint_names_urbubing.yaml
[0.067s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//config/rviz/display_model.py
[0.067s] -- Symlinking: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//config/rviz/urbubing_display.rviz
[0.074s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo//worlds/test_world.world
[0.074s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/ament_index/resource_index/package_run_dependencies/pkg_gazebo
[0.074s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/ament_index/resource_index/parent_prefix_path/pkg_gazebo
[0.074s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/environment/ament_prefix_path.sh
[0.074s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/environment/ament_prefix_path.dsv
[0.074s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/environment/path.sh
[0.075s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/environment/path.dsv
[0.075s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/local_setup.bash
[0.075s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/local_setup.sh
[0.075s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/local_setup.zsh
[0.075s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/local_setup.dsv
[0.075s] -- Symlinking: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/package.dsv
[0.082s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/ament_index/resource_index/packages/pkg_gazebo
[0.082s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/cmake/pkg_gazeboConfig.cmake
[0.082s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/cmake/pkg_gazeboConfig-version.cmake
[0.082s] -- Up-to-date symlink: /home/<USER>/robot_1/install/pkg_gazebo/share/pkg_gazebo/package.xml
[0.084s] Invoked command in '/home/<USER>/robot_1/build/pkg_gazebo' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/dev_ws/install/learning_urdf:/home/<USER>/dev_ws/install/learning_topic:/home/<USER>/dev_ws/install/learning_tf:/home/<USER>/dev_ws/install/learning_service:/home/<USER>/dev_ws/install/learning_qos:/home/<USER>/dev_ws/install/learning_pkg_python:/home/<USER>/dev_ws/install/learning_parameter:/home/<USER>/dev_ws/install/learning_node:/home/<USER>/dev_ws/install/learning_launch:/home/<USER>/dev_ws/install/learning_action:/home/<USER>/dev_ws/install/learning_gazebo_harmonic:/home/<USER>/dev_ws/install/learning_gazebo:/home/<USER>/dev_ws/install/learning_cv:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/robot_1/build/pkg_gazebo
