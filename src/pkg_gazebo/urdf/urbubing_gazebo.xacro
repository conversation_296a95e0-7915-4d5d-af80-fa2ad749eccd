<?xml version="1.0"?>
<robot name="urbubing" xmlns:xacro="http://www.ros.org/wiki/xacro">

  <!-- 包含宏定义文件 -->
  <xacro:include filename="$(find pkg_gazebo)/urdf/urbubing_macros.xacro" />

  <!-- 基座链接 - 使用盒子碰撞 -->
  <link name="base_link">
    <xacro:inertial_block 
      mass="8.86791370010152"
      ixx="0.0142338465075317" ixy="0.000558944709505001" ixz="-6.45174465414214E-05"
      iyy="0.020235487366205" iyz="2.48760063799849E-06" izz="0.0310177616926185"
      origin_xyz="0.0432971520727676 0.0105027413251315 -0.0234599741467773"
      origin_rpy="0 0 0" />
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="file://$(find pkg_gazebo)/meshes/base_link.STL" />
      </geometry>
      <xacro:material_base />
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <box size="0.4 0.3 0.15" />
      </geometry>
    </collision>
  </link>

  <!-- 轮子宏定义 - 使用圆柱体碰撞 -->
  <xacro:macro name="wheel_link_simple" params="name mass ixx ixy ixz iyy iyz izz origin_xyz origin_rpy">
    <link name="${name}">
      <xacro:inertial_block 
        mass="${mass}"
        ixx="${ixx}" ixy="${ixy}" ixz="${ixz}"
        iyy="${iyy}" iyz="${iyz}" izz="${izz}"
        origin_xyz="${origin_xyz}"
        origin_rpy="${origin_rpy}" />
      <visual>
        <origin xyz="0 0 0" rpy="0 0 0" />
        <geometry>
          <mesh filename="file://$(find pkg_gazebo)/meshes/${name}.STL" />
        </geometry>
        <xacro:material_white />
      </visual>
      <collision>
        <origin xyz="0 0 0" rpy="1.5708 0 0" />
        <geometry>
          <cylinder radius="0.08" length="0.05" />
        </geometry>
      </collision>
    </link>
  </xacro:macro>

  <!-- 左前轮 -->
  <xacro:wheel_link_simple 
    name="wheel_lf_Link"
    mass="0.254885792947332"
    ixx="0.000108939063406329" ixy="-1.15453156708273E-06" ixz="-8.45799335200679E-08"
    iyy="0.00010634846361049" iyz="-5.94653917928581E-08" izz="0.000191411121109834"
    origin_xyz="0.000265345929880895 -0.000118708607752468 -0.022639772047863"
    origin_rpy="0 0 0" />

  <xacro:continuous_joint 
    name="wheel_lf_Joint"
    parent="base_link"
    child="wheel_lf_Link"
    origin_xyz="-0.19103 -0.17579 -0.073501"
    origin_rpy="1.5708 0 -3.1416"
    axis_xyz="0 0 -1" />

  <!-- 右前轮 -->
  <xacro:wheel_link_simple 
    name="wheel_rf_Link"
    mass="0.254885528480109"
    ixx="0.000109611099518635" ixy="7.22345430664628E-07" ixz="-1.01734664568465E-07"
    iyy="0.000105674085148553" iyz="-1.84111861837377E-08" izz="0.000191408781189104"
    origin_xyz="0.000290444031789294 4.72961132246541E-05 -0.022639772047863"
    origin_rpy="0 0 0" />

  <xacro:continuous_joint 
    name="wheel_rf_Joint"
    parent="base_link"
    child="wheel_rf_Link"
    origin_xyz="-0.19103 0.17321 -0.073501"
    origin_rpy="1.5708 0 3.1416"
    axis_xyz="0 0 -1" />

  <!-- 右后轮 -->
  <xacro:wheel_link_simple 
    name="wheel_rb_Link"
    mass="0.254885792415455"
    ixx="0.000108939063843945" ixy="1.15453062837799E-06" ixz="-8.45794688965599E-08"
    iyy="0.000106348461840995" iyz="5.94656489436515E-08" izz="0.000191411119846991"
    origin_xyz="-0.000265345907702663 -0.000118708674948537 -0.022639772047863"
    origin_rpy="0 0 0" />

  <xacro:continuous_joint 
    name="wheel_rb_Joint"
    parent="base_link"
    child="wheel_rb_Link"
    origin_xyz="0.18833 0.17052 -0.073501"
    origin_rpy="1.5708 0 -3.1416"
    axis_xyz="0 0 -1" />

  <!-- 左后轮 -->
  <xacro:wheel_link_simple 
    name="wheel_lb_Link"
    mass="0.254885519917945"
    ixx="0.000109730634608958" ixy="-1.92423965660535E-07" ixz="1.03294821169717E-07"
    iyy="0.000105554640970902" iyz="-4.42030493604287E-09" izz="0.000191408866690139"
    origin_xyz="-0.000293733140556973 1.77530441537038E-05 -0.022639772047863"
    origin_rpy="0 0 0" />

  <xacro:continuous_joint 
    name="wheel_lb_Joint"
    parent="base_link"
    child="wheel_lb_Link"
    origin_xyz="0.18788 -0.17579 -0.073501"
    origin_rpy="1.5708 0 3.1416"
    axis_xyz="0 0 -1" />

  <!-- 偏航链接 - 使用圆柱体碰撞 -->
  <link name="yaw_Link">
    <xacro:inertial_block 
      mass="1.34574810542162"
      ixx="0.00119254241296655" ixy="-4.90978683056211E-07" ixz="2.50918559511305E-06"
      iyy="0.00127302578166741" iyz="-9.62484462634763E-07" izz="0.00153599058748134"
      origin_xyz="-0.00105608378572911 0.00375450379687958 -0.0921528912156362"
      origin_rpy="0 0 0" />
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="file://$(find pkg_gazebo)/meshes/yaw_Link.STL" />
      </geometry>
      <xacro:material_white />
    </visual>
    <collision>
      <origin xyz="0 0 -0.1" rpy="0 0 0" />
      <geometry>
        <cylinder radius="0.1" length="0.2" />
      </geometry>
    </collision>
  </link>

  <xacro:continuous_joint 
    name="yaw_Joint"
    parent="base_link"
    child="yaw_Link"
    origin_xyz="-0.0014015 -0.0018662 -0.050192"
    origin_rpy="3.1416 0 0"
    axis_xyz="0 0 -1" />

  <!-- 表盘链接 - 使用圆柱体碰撞 -->
  <link name="dials_Link">
    <xacro:inertial_block 
      mass="0.191607659470622"
      ixx="0.000133402350419207" ixy="-2.17730459620246E-06" ixz="3.83907510541521E-10"
      iyy="0.000131171244191974" iyz="6.80021326733225E-10" izz="0.000254090705830729"
      origin_xyz="-0.000401308990043536 -0.000614658686248881 0.0206666698462531"
      origin_rpy="0 0 0" />
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="file://$(find pkg_gazebo)/meshes/dials_Link.STL" />
      </geometry>
      <xacro:material_white />
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <cylinder radius="0.05" length="0.04" />
      </geometry>
    </collision>
  </link>

  <xacro:continuous_joint 
    name="dials_Joint"
    parent="yaw_Link"
    child="dials_Link"
    origin_xyz="-0.064978 -0.0010679 -0.2856"
    origin_rpy="0 0 0"
    axis_xyz="-0.10617 0.0076706 -0.99432" />

  <!-- Gazebo物理属性 -->
  <xacro:base_gazebo_properties link_name="base_link" />
  <xacro:wheel_gazebo_properties link_name="wheel_lf_Link" />
  <xacro:wheel_gazebo_properties link_name="wheel_rf_Link" />
  <xacro:wheel_gazebo_properties link_name="wheel_rb_Link" />
  <xacro:wheel_gazebo_properties link_name="wheel_lb_Link" />
  <xacro:gazebo_link_properties link_name="yaw_Link" material="Gazebo/White" />
  <xacro:gazebo_link_properties link_name="dials_Link" material="Gazebo/White" />

  <!-- Gazebo Harmonic 控制器插件 -->
  <gazebo>
    <plugin filename="gz-sim-joint-state-publisher-system" name="gz::sim::systems::JointStatePublisher">
    </plugin>
    <plugin filename="gz-sim-pose-publisher-system" name="gz::sim::systems::PosePublisher">
      <publish_link_pose>true</publish_link_pose>
      <use_pose_vector_msg>true</use_pose_vector_msg>
      <static_publisher>true</static_publisher>
      <static_update_frequency>1</static_update_frequency>
    </plugin>
  </gazebo>

</robot>
