<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro">

  <!-- 常用参数定义 -->
  <xacro:property name="package_name" value="pkg_gazebo" />
  <xacro:property name="mesh_path" value="package://${package_name}/meshes" />
  <!-- Gazebo Harmonic兼容的网格路径 -->
  <xacro:property name="mesh_path_file" value="file://$(find pkg_gazebo)/meshes" />
  
  <!-- 材料定义 -->
  <xacro:property name="base_color" value="0.752941176470588 0.752941176470588 0.752941176470588 1" />
  <xacro:property name="white_color" value="1 1 1 1" />
  <xacro:property name="aid_color" value="0.776470588235294 0.756862745098039 0.737254901960784 1" />
  
  <!-- 关节限制参数 -->
  <xacro:property name="default_effort" value="100" />
  <xacro:property name="default_velocity" value="100" />
  
  <!-- 材料宏定义 -->
  <xacro:macro name="material_base">
    <material name="">
      <color rgba="${base_color}" />
    </material>
  </xacro:macro>
  
  <xacro:macro name="material_white">
    <material name="">
      <color rgba="${white_color}" />
    </material>
  </xacro:macro>
  
  <xacro:macro name="material_aid">
    <material name="">
      <color rgba="${aid_color}" />
    </material>
  </xacro:macro>
  
  <!-- 惯性宏定义 -->
  <xacro:macro name="inertial_block" params="mass ixx ixy ixz iyy iyz izz origin_xyz origin_rpy">
    <inertial>
      <origin xyz="${origin_xyz}" rpy="${origin_rpy}" />
      <mass value="${mass}" />
      <inertia
        ixx="${ixx}"
        ixy="${ixy}"
        ixz="${ixz}"
        iyy="${iyy}"
        iyz="${iyz}"
        izz="${izz}" />
    </inertial>
  </xacro:macro>
  
  <!-- 视觉和碰撞宏定义 -->
  <xacro:macro name="visual_collision_block" params="mesh_file material_macro">
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="${mesh_path}/${mesh_file}" />
      </geometry>
      <xacro:call macro="${material_macro}" />
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="${mesh_path}/${mesh_file}" />
      </geometry>
    </collision>
  </xacro:macro>

  <!-- Gazebo兼容的视觉和碰撞宏定义 -->
  <xacro:macro name="visual_collision_block_gazebo" params="mesh_file material_macro">
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="${mesh_path_file}/${mesh_file}" />
      </geometry>
      <xacro:call macro="${material_macro}" />
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="${mesh_path_file}/${mesh_file}" />
      </geometry>
    </collision>
  </xacro:macro>

  <!-- 简化碰撞几何宏定义 -->
  <xacro:macro name="visual_simple_collision_block" params="mesh_file material_macro collision_geometry">
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="${mesh_path_file}/${mesh_file}" />
      </geometry>
      <xacro:call macro="${material_macro}" />
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        ${collision_geometry}
      </geometry>
    </collision>
  </xacro:macro>
  
  <!-- 轮子宏定义 -->
  <xacro:macro name="wheel_link" params="name mass ixx ixy ixz iyy iyz izz origin_xyz origin_rpy">
    <link name="${name}">
      <xacro:inertial_block 
        mass="${mass}"
        ixx="${ixx}" ixy="${ixy}" ixz="${ixz}"
        iyy="${iyy}" iyz="${iyz}" izz="${izz}"
        origin_xyz="${origin_xyz}"
        origin_rpy="${origin_rpy}" />
      <xacro:visual_collision_block 
        mesh_file="${name}.STL"
        material_macro="material_white" />
    </link>
  </xacro:macro>
  
  <!-- 连续关节宏定义 -->
  <xacro:macro name="continuous_joint" params="name parent child origin_xyz origin_rpy axis_xyz">
    <joint name="${name}" type="continuous">
      <origin xyz="${origin_xyz}" rpy="${origin_rpy}" />
      <parent link="${parent}" />
      <child link="${child}" />
      <axis xyz="${axis_xyz}" />
      <limit effort="${default_effort}" velocity="${default_velocity}" />
    </joint>
  </xacro:macro>
  
  <!-- 旋转关节宏定义 -->
  <xacro:macro name="revolute_joint" params="name parent child origin_xyz origin_rpy axis_xyz lower upper">
    <joint name="${name}" type="revolute">
      <origin xyz="${origin_xyz}" rpy="${origin_rpy}" />
      <parent link="${parent}" />
      <child link="${child}" />
      <axis xyz="${axis_xyz}" />
      <limit
        lower="${lower}"
        upper="${upper}"
        effort="${default_effort}"
        velocity="${default_velocity}" />
    </joint>
  </xacro:macro>

  <!-- Gazebo物理属性宏定义 -->
  <xacro:macro name="gazebo_link_properties" params="link_name material mu1:=0.8 mu2:=0.8 kp:=1000000 kd:=100">
    <gazebo reference="${link_name}">
      <material>${material}</material>
      <mu1>${mu1}</mu1>
      <mu2>${mu2}</mu2>
      <kp>${kp}</kp>
      <kd>${kd}</kd>
    </gazebo>
  </xacro:macro>

  <!-- 轮子Gazebo属性宏 -->
  <xacro:macro name="wheel_gazebo_properties" params="link_name">
    <gazebo reference="${link_name}">
      <material>Gazebo/Black</material>
      <mu1>1.0</mu1>
      <mu2>1.0</mu2>
      <kp>1000000</kp>
      <kd>100</kd>
      <fdir1>1 0 0</fdir1>
    </gazebo>
  </xacro:macro>

  <!-- 基座Gazebo属性宏 -->
  <xacro:macro name="base_gazebo_properties" params="link_name">
    <gazebo reference="${link_name}">
      <material>Gazebo/Grey</material>
      <mu1>0.2</mu1>
      <mu2>0.2</mu2>
      <kp>1000000</kp>
      <kd>100</kd>
    </gazebo>
  </xacro:macro>

  <!-- Gazebo Harmonic 关节控制宏定义 -->
  <xacro:macro name="joint_control" params="joint_name">
    <gazebo reference="${joint_name}">
      <provideFeedback>true</provideFeedback>
    </gazebo>
  </xacro:macro>

  <!-- 关节传动宏定义（保留用于兼容性） -->
  <xacro:macro name="joint_transmission" params="joint_name">
    <transmission name="${joint_name}_transmission">
      <type>transmission_interface/SimpleTransmission</type>
      <joint name="${joint_name}">
        <hardwareInterface>hardware_interface/VelocityJointInterface</hardwareInterface>
      </joint>
      <actuator name="${joint_name}_motor">
        <hardwareInterface>hardware_interface/VelocityJointInterface</hardwareInterface>
        <mechanicalReduction>1</mechanicalReduction>
      </actuator>
    </transmission>
  </xacro:macro>

</robot>
