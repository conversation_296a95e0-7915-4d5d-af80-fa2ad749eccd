<?xml version="1.0"?>
<robot name="urbubing" xmlns:xacro="http://www.ros.org/wiki/xacro">

  <!-- 包含宏定义文件 -->
  <xacro:include filename="$(find pkg_gazebo)/urdf/urbubing_macros.xacro" />

  <!-- 基座链接 -->
  <link name="base_link">
    <xacro:inertial_block 
      mass="8.86791370010152"
      ixx="0.0142338465075317" ixy="0.000558944709505001" ixz="-6.45174465414214E-05"
      iyy="0.020235487366205" iyz="2.48760063799849E-06" izz="0.0310177616926185"
      origin_xyz="0.0432971520727676 0.0105027413251315 -0.0234599741467773"
      origin_rpy="0 0 0" />
    <xacro:visual_collision_block 
      mesh_file="base_link.STL"
      material_macro="material_base" />
  </link>

  <!-- 左前轮 -->
  <xacro:wheel_link 
    name="wheel_lf_Link"
    mass="0.254885792947332"
    ixx="0.000108939063406329" ixy="-1.15453156708273E-06" ixz="-8.45799335200679E-08"
    iyy="0.00010634846361049" iyz="-5.94653917928581E-08" izz="0.000191411121109834"
    origin_xyz="0.000265345929880895 -0.000118708607752468 -0.022639772047863"
    origin_rpy="0 0 0" />

  <xacro:continuous_joint 
    name="wheel_lf_Joint"
    parent="base_link"
    child="wheel_lf_Link"
    origin_xyz="-0.19103 -0.17579 -0.073501"
    origin_rpy="1.5708 0 -3.1416"
    axis_xyz="0 0 -1" />

  <!-- 右前轮 -->
  <xacro:wheel_link 
    name="wheel_rf_Link"
    mass="0.254885528480109"
    ixx="0.000109611099518635" ixy="7.22345430664628E-07" ixz="-1.01734664568465E-07"
    iyy="0.000105674085148553" iyz="-1.84111861837377E-08" izz="0.000191408781189104"
    origin_xyz="0.000290444031789294 4.72961132246541E-05 -0.022639772047863"
    origin_rpy="0 0 0" />

  <xacro:continuous_joint 
    name="wheel_rf_Joint"
    parent="base_link"
    child="wheel_rf_Link"
    origin_xyz="-0.19103 0.17321 -0.073501"
    origin_rpy="1.5708 0 3.1416"
    axis_xyz="0 0 -1" />

  <!-- 右后轮 -->
  <xacro:wheel_link 
    name="wheel_rb_Link"
    mass="0.254885792415455"
    ixx="0.000108939063843945" ixy="1.15453062837799E-06" ixz="-8.45794688965599E-08"
    iyy="0.000106348461840995" iyz="5.94656489436515E-08" izz="0.000191411119846991"
    origin_xyz="-0.000265345907702663 -0.000118708674948537 -0.022639772047863"
    origin_rpy="0 0 0" />

  <xacro:continuous_joint 
    name="wheel_rb_Joint"
    parent="base_link"
    child="wheel_rb_Link"
    origin_xyz="0.18833 0.17052 -0.073501"
    origin_rpy="1.5708 0 -3.1416"
    axis_xyz="0 0 -1" />

  <!-- 左后轮 -->
  <xacro:wheel_link 
    name="wheel_lb_Link"
    mass="0.254885519917945"
    ixx="0.000109730634608958" ixy="-1.92423965660535E-07" ixz="1.03294821169717E-07"
    iyy="0.000105554640970902" iyz="-4.42030493604287E-09" izz="0.000191408866690139"
    origin_xyz="-0.000293733140556973 1.77530441537038E-05 -0.022639772047863"
    origin_rpy="0 0 0" />

  <xacro:continuous_joint
    name="wheel_lb_Joint"
    parent="base_link"
    child="wheel_lb_Link"
    origin_xyz="0.18788 -0.17579 -0.073501"
    origin_rpy="1.5708 0 3.1416"
    axis_xyz="0 0 -1" />

  <!-- 偏航链接 -->
  <link name="yaw_Link">
    <xacro:inertial_block
      mass="1.34574810542162"
      ixx="0.00119254241296655" ixy="-4.90978683056211E-07" ixz="2.50918559511305E-06"
      iyy="0.00127302578166741" iyz="-9.62484462634763E-07" izz="0.00153599058748134"
      origin_xyz="-0.00105608378572911 0.00375450379687958 -0.0921528912156362"
      origin_rpy="0 0 0" />
    <xacro:visual_collision_block
      mesh_file="yaw_Link.STL"
      material_macro="material_white" />
  </link>

  <xacro:continuous_joint
    name="yaw_Joint"
    parent="base_link"
    child="yaw_Link"
    origin_xyz="-0.0014015 -0.0018662 -0.050192"
    origin_rpy="3.1416 0 0"
    axis_xyz="0 0 -1" />

  <!-- 俯仰链接 -->
  <link name="pitch_Link">
    <xacro:inertial_block
      mass="0.232186075150672"
      ixx="7.30073800729081E-05" ixy="-5.14621394946364E-07" ixz="-2.31916909158694E-07"
      iyy="7.55479055414904E-05" iyz="4.26271355145143E-07" izz="0.000110005445018448"
      origin_xyz="-0.000660825931109735 0.000125747113190827 0.0368612940049887"
      origin_rpy="0 0 0" />
    <xacro:visual_collision_block
      mesh_file="pitch_Link.STL"
      material_macro="material_white" />
  </link>

  <xacro:revolute_joint
    name="pitch_Joint"
    parent="yaw_Link"
    child="pitch_Link"
    origin_xyz="0.00047466 -0.059676 -0.1331"
    origin_rpy="-1.5708 0 0.0071369"
    axis_xyz="0 0 -1"
    lower="-0.7854"
    upper="0.5236" />

  <!-- 俯仰辅助链接1 -->
  <link name="pitch_aid_Link1">
    <xacro:inertial_block
      mass="0.0284426822264771"
      ixx="2.61361437949981E-05" ixy="-2.50931180327187E-19" ixz="-1.15374767907533E-09"
      iyy="1.01177265417115E-06" iyz="-2.25697231145407E-21" izz="2.6297792852574E-05"
      origin_xyz="4.33362215481875E-05 0.0376810264702473 -0.00607205227133864"
      origin_rpy="0 0 0" />
    <xacro:visual_collision_block
      mesh_file="pitch_aid_Link1.STL"
      material_macro="material_aid" />
  </link>

  <xacro:revolute_joint
    name="pitch_aid_Joint1"
    parent="pitch_Link"
    child="pitch_aid_Link1"
    origin_xyz="-0.04 0 0.0015"
    origin_rpy="0 0.0071369 0"
    axis_xyz="0.0071368 0 -0.99997"
    lower="-1.5708"
    upper="1.5078" />

  <!-- 俯仰辅助链接2 -->
  <link name="pitch_aid_Link2">
    <xacro:inertial_block
      mass="0.0525879808870972"
      ixx="5.26795907742542E-06" ixy="5.54023325186128E-07" ixz="-6.16868287450195E-19"
      iyy="1.18232350981482E-05" iyz="2.98943073376664E-20" izz="1.58800109121266E-05"
      origin_xyz="0.0231873221663826 -0.00269569306105638 0.0006382093745253"
      origin_rpy="0 0 0" />
    <xacro:visual_collision_block
      mesh_file="pitch_aid_Link2.STL"
      material_macro="material_aid" />
  </link>

  <xacro:revolute_joint
    name="pitch_aid_Joint2"
    parent="pitch_aid_Link1"
    child="pitch_aid_Link2"
    origin_xyz="0.00010777 0.121 -0.0151"
    origin_rpy="0 -0.0071369 0"
    axis_xyz="0 0 -1"
    lower="-0.5236"
    upper="0.7854" />

  <!-- 俯仰辅助链接3 -->
  <link name="pitch_aid_Link3">
    <xacro:inertial_block
      mass="1.57956286586645"
      ixx="0.000820610095467701" ixy="1.96050511500634E-05" ixz="-1.05211974788944E-06"
      iyy="0.00162834327540048" iyz="4.04798144959797E-07" izz="0.00109666681474034"
      origin_xyz="0.00998594838506346 0.0101003265745883 0.0699748929192421"
      origin_rpy="0 0 0" />
    <xacro:visual_collision_block
      mesh_file="pitch_aid_Link3.STL"
      material_macro="material_aid" />
  </link>

  <xacro:revolute_joint
    name="pitch_aid_Joint3"
    parent="pitch_aid_Link2"
    child="pitch_aid_Link3"
    origin_xyz="0.04 0 0"
    origin_rpy="0 0 0"
    axis_xyz="0 0 -1"
    lower="-0.5236"
    upper="0.7854" />

  <!-- 表盘链接 -->
  <link name="dials_Link">
    <xacro:inertial_block
      mass="0.191607659470622"
      ixx="0.000133402350419207" ixy="-2.17730459620246E-06" ixz="3.83907510541521E-10"
      iyy="0.000131171244191974" iyz="6.80021326733225E-10" izz="0.000254090705830729"
      origin_xyz="-0.000401308990043536 -0.000614658686248881 0.0206666698462531"
      origin_rpy="0 0 0" />
    <xacro:visual_collision_block
      mesh_file="dials_Link.STL"
      material_macro="material_white" />
  </link>

  <xacro:continuous_joint
    name="dials_Joint"
    parent="yaw_Link"
    child="dials_Link"
    origin_xyz="-0.064978 -0.0010679 -0.2856"
    origin_rpy="0 0 0"
    axis_xyz="-0.10617 0.0076706 -0.99432" />

  <!-- Gazebo物理属性 -->
  <xacro:base_gazebo_properties link_name="base_link" />

  <xacro:wheel_gazebo_properties link_name="wheel_lf_Link" />
  <xacro:wheel_gazebo_properties link_name="wheel_rf_Link" />
  <xacro:wheel_gazebo_properties link_name="wheel_rb_Link" />
  <xacro:wheel_gazebo_properties link_name="wheel_lb_Link" />

  <xacro:gazebo_link_properties link_name="yaw_Link" material="Gazebo/White" />
  <xacro:gazebo_link_properties link_name="pitch_Link" material="Gazebo/White" />
  <xacro:gazebo_link_properties link_name="pitch_aid_Link1" material="Gazebo/Wood" />
  <xacro:gazebo_link_properties link_name="pitch_aid_Link2" material="Gazebo/Wood" />
  <xacro:gazebo_link_properties link_name="pitch_aid_Link3" material="Gazebo/Wood" />
  <xacro:gazebo_link_properties link_name="dials_Link" material="Gazebo/White" />

  <!-- 关节传动 -->
  <xacro:joint_transmission joint_name="wheel_lf_Joint" />
  <xacro:joint_transmission joint_name="wheel_rf_Joint" />
  <xacro:joint_transmission joint_name="wheel_rb_Joint" />
  <xacro:joint_transmission joint_name="wheel_lb_Joint" />
  <xacro:joint_transmission joint_name="yaw_Joint" />
  <xacro:joint_transmission joint_name="pitch_Joint" />

  <!-- Gazebo Harmonic 控制器插件 -->
  <gazebo>
    <plugin filename="gz-sim-joint-state-publisher-system" name="gz::sim::systems::JointStatePublisher">
    </plugin>
    <plugin filename="gz-sim-pose-publisher-system" name="gz::sim::systems::PosePublisher">
      <publish_link_pose>true</publish_link_pose>
      <use_pose_vector_msg>true</use_pose_vector_msg>
      <static_publisher>true</static_publisher>
      <static_update_frequency>1</static_update_frequency>
    </plugin>
  </gazebo>

</robot>
