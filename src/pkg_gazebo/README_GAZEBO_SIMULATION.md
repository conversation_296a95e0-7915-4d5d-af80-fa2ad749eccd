# Gazebo仿真系统使用指南

## 概述
本文档介绍如何使用完整配置的Gazebo仿真系统来加载和测试urbubing机器人模型。

## 系统组件

### 1. 测试世界文件
- **文件**: `worlds/test_world.world`
- **内容**: 包含地面、基础照明和测试物体的仿真环境
- **特点**: 
  - 地面平面用于机器人移动
  - 多种几何形状的测试物体（盒子、圆柱体、球体、墙壁）
  - 适当的光照配置
  - 物理引擎配置

### 2. 机器人模型
- **文件**: `urdf/urbubing_gazebo.xacro`
- **特点**:
  - 使用`file://`路径解决网格文件加载问题
  - 简化的碰撞几何以提高物理仿真性能
  - 完整的Gazebo材料和物理属性配置
  - 集成的关节状态发布器和位姿发布器插件

### 3. 启动配置
- **文件**: `launch/gazebo.launch.py`
- **功能**:
  - 自动加载测试世界
  - 配置机器人状态发布器
  - 设置ROS-Gazebo桥接器
  - 正确的资源路径配置

## 使用方法

### 启动仿真系统
```bash
# 构建项目
colcon build --packages-select pkg_gazebo

# 加载环境
source install/setup.bash

# 启动Gazebo仿真
ros2 launch pkg_gazebo gazebo.launch.py
```

### 可选参数
```bash
# 启动时包含关节状态发布器GUI
ros2 launch pkg_gazebo gazebo.launch.py gui:=true
```

### 验证系统状态
```bash
# 检查ROS话题
ros2 topic list

# 查看机器人描述
ros2 param get /robot_state_publisher robot_description

# 监控关节状态（如果可用）
ros2 topic echo /joint_states

# 查看TF变换
ros2 topic echo /tf_static
```

## 系统架构

### ROS话题
- `/clock` - 仿真时钟
- `/robot_description` - 机器人URDF描述
- `/joint_states` - 关节状态信息
- `/tf` - 动态坐标变换
- `/tf_static` - 静态坐标变换

### Gazebo插件
- **JointStatePublisher**: 发布关节状态到ROS
- **PosePublisher**: 发布链接位姿信息
- **Physics**: Dartsim物理引擎
- **SceneBroadcaster**: 场景信息广播

### 桥接器配置
- 时钟同步: Gazebo时钟 → ROS时钟
- TF变换: Gazebo位姿 → ROS TF
- 关节状态: Gazebo模型 → ROS关节状态

## 测试环境

### 测试物体
1. **红色盒子** (2, 0, 0.5) - 碰撞测试
2. **绿色圆柱体** (-2, 2, 0.5) - 形状交互测试
3. **蓝色球体** (0, -3, 0.5) - 滚动物理测试
4. **灰色墙壁** (5, 0, 1) - 导航障碍测试

### 机器人初始位置
- 位置: (0, 0, 0.15)
- 朝向: (0, 0, 0)
- 高度调整以适应轮子接触地面

## 故障排除

### 常见问题

1. **机器人模型不可见**
   - 检查网格文件是否存在于`meshes/`目录
   - 确认资源路径配置正确
   - 验证XACRO文件语法

2. **物理仿真异常**
   - 检查碰撞几何配置
   - 验证惯性参数设置
   - 确认物理引擎插件加载

3. **ROS集成问题**
   - 检查桥接器状态
   - 验证话题发布
   - 确认时钟同步

### 调试命令
```bash
# 检查Gazebo进程
ps aux | grep gz

# 验证XACRO处理
xacro src/pkg_gazebo/urdf/urbubing_gazebo.xacro

# 测试仿真系统
python3 src/pkg_gazebo/scripts/test_simulation.py
```

## 技术细节

### 文件结构
```
src/pkg_gazebo/
├── worlds/
│   └── test_world.world          # 测试世界文件
├── urdf/
│   ├── urbubing_gazebo.xacro     # Gazebo专用机器人模型
│   └── urbubing_macros.xacro     # 宏定义文件
├── launch/
│   └── gazebo.launch.py          # 主启动文件
├── meshes/
│   └── *.STL                     # 3D网格文件
└── scripts/
    └── test_simulation.py        # 测试脚本
```

### 关键改进
1. **网格路径修复**: `package://` → `file://$(find pkg_gazebo)/`
2. **碰撞几何优化**: 复杂STL → 简单几何形状
3. **资源路径配置**: 正确的Gazebo资源路径设置
4. **插件集成**: 现代Gazebo Harmonic插件系统

## 下一步开发建议

1. **控制器集成**: 添加差速驱动控制器
2. **传感器仿真**: 集成激光雷达、相机等传感器
3. **导航功能**: 配置导航堆栈
4. **自定义世界**: 创建特定应用场景的世界文件
5. **性能优化**: 调整物理参数以提高仿真效率

## 成功指标

✅ Gazebo成功启动并加载测试世界  
✅ 机器人模型正确显示在3D环境中  
✅ 网格文件正确加载和渲染  
✅ 物理仿真正常工作  
✅ ROS-Gazebo桥接器正常运行  
✅ 机器人描述参数正确发布  
✅ 系统集成测试通过  

🎉 **恭喜！您的Gazebo仿真系统已成功配置并可用于机器人开发和测试！**
