import os
import launch
import launch_ros
from ament_index_python.packages import get_package_share_directory
from launch.actions import IncludeLaunchDescription, TimerAction, SetEnvironmentVariable, ExecuteProcess
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution


def generate_launch_description():
    # 获取包路径
    pkg_gazebo = get_package_share_directory('pkg_gazebo')
    
    # 获取XACRO文件路径 - 使用最终优化版本
    xacro_file = os.path.join(pkg_gazebo, 'urdf', 'urbubing_final.xacro')
    
    # 设置Gazebo资源路径
    gazebo_resource_paths = [
        '/opt/ros/jazzy/share',
        pkg_gazebo,
        os.path.dirname(pkg_gazebo),
        '/home/<USER>/robot_1/install',
        '/home/<USER>/robot_1/install/pkg_gazebo/share'
    ]
    
    set_gazebo_resource_path = SetEnvironmentVariable(
        'GZ_SIM_RESOURCE_PATH', 
        ':'.join(gazebo_resource_paths)
    )
    
    # 声明启动参数
    declare_use_sim_time_cmd = launch.actions.DeclareLaunchArgument(
        'use_sim_time',
        default_value='true',
        description='Use simulation (Gazebo) clock if true')
    
    # 机器人描述参数
    robot_description = launch_ros.parameter_descriptions.ParameterValue(
        launch.substitutions.Command(['xacro ', xacro_file]),
        value_type=str)
    
    # 机器人状态发布节点
    robot_state_publisher_node = launch_ros.actions.Node(
        package='robot_state_publisher',
        executable='robot_state_publisher',
        name='robot_state_publisher',
        output='screen',
        parameters=[{
            'use_sim_time': LaunchConfiguration('use_sim_time'),
            'robot_description': robot_description
        }]
    )
    
    # 启动Gazebo - 使用直接命令
    start_gazebo_cmd = ExecuteProcess(
        cmd=['gz', 'sim', '-r', '-v', '4', 'empty.sdf'],
        output='screen',
        additional_env={
            'GZ_SIM_RESOURCE_PATH': ':'.join(gazebo_resource_paths)
        }
    )
    
    # 延迟生成机器人，确保Gazebo完全启动
    spawn_entity_cmd = TimerAction(
        period=5.0,
        actions=[
            launch_ros.actions.Node(
                package='ros_gz_sim',
                executable='create',
                arguments=[
                    '-topic', 'robot_description',
                    '-name', 'urbubing',
                    '-x', '0.0',
                    '-y', '0.0',
                    '-z', '0.2'
                ],
                output='screen'
            )
        ]
    )
    
    # ROS-Gazebo桥接器
    bridge_cmd = launch_ros.actions.Node(
        package='ros_gz_bridge',
        executable='parameter_bridge',
        arguments=[
            '/clock@rosgraph_msgs/msg/Clock[gz.msgs.Clock',
        ],
        output='screen'
    )
    
    return launch.LaunchDescription([
        set_gazebo_resource_path,
        declare_use_sim_time_cmd,
        robot_state_publisher_node,
        start_gazebo_cmd,
        spawn_entity_cmd,
        bridge_cmd,
    ])
