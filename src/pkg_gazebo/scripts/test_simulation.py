#!/usr/bin/env python3
"""
测试Gazebo仿真系统的脚本
验证机器人模型是否正确加载和运行
"""

import rclpy
from rclpy.node import Node
from sensor_msgs.msg import JointState
from geometry_msgs.msg import Twist
from std_msgs.msg import String
import time

class SimulationTester(Node):
    def __init__(self):
        super().__init__('simulation_tester')
        
        # 订阅者
        self.joint_state_sub = self.create_subscription(
            JointState,
            '/joint_states',
            self.joint_state_callback,
            10
        )
        
        self.robot_desc_sub = self.create_subscription(
            String,
            '/robot_description',
            self.robot_desc_callback,
            10
        )
        
        # 状态变量
        self.joint_states_received = False
        self.robot_desc_received = False
        self.joint_names = []
        
        self.get_logger().info('仿真测试器已启动')
        
    def joint_state_callback(self, msg):
        if not self.joint_states_received:
            self.joint_states_received = True
            self.joint_names = msg.name
            self.get_logger().info(f'接收到关节状态，关节数量: {len(msg.name)}')
            self.get_logger().info(f'关节名称: {msg.name}')
            
    def robot_desc_callback(self, msg):
        if not self.robot_desc_received:
            self.robot_desc_received = True
            self.get_logger().info('接收到机器人描述')
            
    def run_test(self):
        """运行测试序列"""
        self.get_logger().info('开始测试序列...')
        
        # 等待消息
        timeout = 10.0
        start_time = time.time()
        
        while (time.time() - start_time) < timeout:
            rclpy.spin_once(self, timeout_sec=0.1)
            
            if self.joint_states_received and self.robot_desc_received:
                break
                
        # 报告结果
        self.get_logger().info('=== 测试结果 ===')
        self.get_logger().info(f'机器人描述接收: {"✅" if self.robot_desc_received else "❌"}')
        self.get_logger().info(f'关节状态接收: {"✅" if self.joint_states_received else "❌"}')
        
        if self.joint_states_received:
            self.get_logger().info(f'检测到 {len(self.joint_names)} 个关节')
            
        if self.robot_desc_received and self.joint_states_received:
            self.get_logger().info('🎉 仿真系统测试通过！')
            return True
        else:
            self.get_logger().error('❌ 仿真系统测试失败')
            return False

def main():
    rclpy.init()
    
    tester = SimulationTester()
    
    try:
        success = tester.run_test()
        if success:
            print("\n✅ Gazebo仿真系统配置成功！")
            print("机器人模型已正确加载到物理仿真环境中。")
        else:
            print("\n❌ 仿真系统存在问题，请检查配置。")
            
    except KeyboardInterrupt:
        pass
    finally:
        tester.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
