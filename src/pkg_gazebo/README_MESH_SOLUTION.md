# Gazebo网格文件显示问题解决方案

## 问题描述
机器人模型在Gazebo Entity Tree中可见，但在3D世界中看不到网格模型。

## 根本原因
1. **网格路径解析问题**: Gazebo Harmonic对`package://`URI的解析方式与旧版本不同
2. **碰撞几何问题**: Dartsim物理引擎不支持复杂的STL网格碰撞
3. **资源路径配置**: 需要正确配置Gazebo资源路径

## 解决方案

### 1. 创建了专用的Gazebo XACRO文件
- `urbubing_final.xacro` - 使用`file://`绝对路径和简化碰撞几何

### 2. 网格路径修复
```xml
<!-- 原来的问题路径 -->
<mesh filename="package://pkg_gazebo/meshes/base_link.STL" />

<!-- 修复后的路径 -->
<mesh filename="file://$(find pkg_gazebo)/meshes/base_link.STL" />
```

### 3. 简化碰撞几何
```xml
<!-- 基座使用盒子碰撞 -->
<collision>
  <geometry>
    <box size="0.4 0.3 0.15" />
  </geometry>
</collision>

<!-- 轮子使用圆柱体碰撞 -->
<collision>
  <geometry>
    <cylinder radius="0.08" length="0.05" />
  </geometry>
</collision>
```

### 4. 环境变量配置
```python
# 在启动文件中设置正确的资源路径
gazebo_resource_paths = [
    '/opt/ros/jazzy/share',
    pkg_gazebo,
    os.path.dirname(pkg_gazebo),
    '/home/<USER>/robot_1/install',
    '/home/<USER>/robot_1/install/pkg_gazebo/share'
]

set_gazebo_resource_path = SetEnvironmentVariable(
    'GZ_SIM_RESOURCE_PATH', 
    ':'.join(gazebo_resource_paths)
)
```

## 使用方法

### 启动完整的Gazebo仿真（推荐）
```bash
ros2 launch pkg_gazebo gazebo_final.launch.py
```

### 其他可用选项
```bash
# RViz显示（原始XACRO）
ros2 launch pkg_gazebo display.launch.py

# Gazebo测试（带网格路径配置）
ros2 launch pkg_gazebo gazebo_with_meshes.launch.py
```

## 验证结果

### ✅ 成功解决的问题
1. **网格文件可见** - 机器人模型在Gazebo 3D世界中正确显示
2. **无路径错误** - 不再出现"Unable to find file"错误
3. **物理仿真正常** - 碰撞检测和物理交互正常工作
4. **材料正确显示** - 所有Gazebo材料正确解析和显示

### 📊 测试输出对比

**修复前:**
```
[Err] [SystemPaths.cc:425] Unable to find file with URI [model://pkg_gazebo/meshes/base_link.STL]
[Err] [MeshManager.cc:211] Unable to find file[model://pkg_gazebo/meshes/base_link.STL]
[Wrn] [Util.cc:859] Failed to load mesh from [model://pkg_gazebo/meshes/base_link.STL]
```

**修复后:**
```
[INFO] [ros_gz_sim]: Entity creation successful.
[Wrn] [SdfEntityCreator.cc:949] Using an internal gazebo.material to parse Gazebo/Grey
```

## 技术细节

### 文件结构
```
src/pkg_gazebo/
├── urdf/
│   ├── urbubing.xacro          # 原始XACRO（RViz用）
│   ├── urbubing_gazebo.xacro   # Gazebo版本（完整网格）
│   ├── urbubing_final.xacro    # 最终版本（简化碰撞）
│   └── urbubing_macros.xacro   # 宏定义
├── launch/
│   ├── display.launch.py       # RViz显示
│   ├── gazebo_with_meshes.launch.py  # 网格测试
│   └── gazebo_final.launch.py  # 最终仿真
└── meshes/
    └── *.STL                   # 网格文件
```

### 关键改进
1. **路径解析**: `package://` → `file://$(find pkg_gazebo)/`
2. **碰撞几何**: 复杂STL网格 → 简单几何形状
3. **资源配置**: 正确设置`GZ_SIM_RESOURCE_PATH`
4. **物理兼容**: 适配Dartsim物理引擎限制

## 注意事项
1. 使用`gazebo_final.launch.py`获得最佳效果
2. 简化的碰撞几何可能与视觉模型略有差异
3. 如需精确碰撞，可考虑创建专门的碰撞网格文件
4. 确保所有STL文件存在于meshes目录中

现在您的机器人模型应该可以在Gazebo中完美显示和仿真！🎉
