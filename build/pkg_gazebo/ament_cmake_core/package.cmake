set(_AMENT_PACKAGE_NAME "pkg_gazebo")
set(pkg_gazebo_VERSION "0.0.0")
set(pkg_gazebo_MAINTAINER "skysky <<EMAIL>>")
set(pkg_gazebo_BUILD_DEPENDS )
set(pkg_gazebo_BUILDTOOL_DEPENDS "ament_cmake")
set(pkg_gazebo_BUILD_EXPORT_DEPENDS )
set(pkg_gazebo_BUILDTOOL_EXPORT_DEPENDS )
set(pkg_gazebo_EXEC_DEPENDS )
set(pkg_gazebo_TEST_DEPENDS "ament_lint_auto" "ament_lint_common")
set(pkg_gazebo_GROUP_DEPENDS )
set(pkg_gazebo_MEMBER_OF_GROUPS )
set(pkg_gazebo_DEPRECATED "")
set(pkg_gazebo_EXPORT_TAGS)
list(APPEND pkg_gazebo_EXPORT_TAGS "<build_type>ament_cmake</build_type>")
